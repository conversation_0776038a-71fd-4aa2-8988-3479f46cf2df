#!/bin/bash
# GameFlex Lambda Hot Reload Deployment Script
# This script deploys Lambda functions using LocalStack CLI with hot reload capability

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}[LAMBDA-HOT-RELOAD]${NC} $1"
}

log_header "GameFlex Lambda Hot Reload Deployment"
echo ""

# Set AWS environment variables for LocalStack
export AWS_ACCESS_KEY_ID=test
export AWS_SECRET_ACCESS_KEY=test
export AWS_DEFAULT_REGION=us-east-1
export AWS_ENDPOINT_URL=http://localhost:45660

# Function to check if LocalStack is running
check_localstack() {
    if curl -s "http://localhost:45660/_localstack/health" > /dev/null 2>&1; then
        return 0
    else
        return 1
    fi
}

# Function to install dependencies for a Lambda function
install_lambda_dependencies() {
    local function_path=$1
    local function_name=$(basename "$function_path")
    
    log_info "Installing dependencies for $function_name..."
    
    cd "$function_path"
    if npm install; then
        log_info "Dependencies installed for $function_name"
        cd - > /dev/null
        return 0
    else
        log_error "Failed to install dependencies for $function_name"
        cd - > /dev/null
        return 1
    fi
}

# Function to build a Lambda function with ESbuild (one-time build)
build_lambda_function() {
    local function_path=$1
    local function_name=$(basename "$function_path")
    
    log_info "Building $function_name Lambda function..."
    
    cd "$function_path"
    
    # Create dist directory if it doesn't exist
    mkdir -p dist
    
    # Run one-time build
    if npm run build-once; then
        log_info "$function_name built successfully"
        cd - > /dev/null
        return 0
    else
        log_error "Failed to build $function_name"
        cd - > /dev/null
        return 1
    fi
}

# Function to start hot reload build for a Lambda function
start_hot_reload_build() {
    local function_path=$1
    local function_name=$(basename "$function_path")
    
    log_info "Starting hot reload build for $function_name..."
    
    cd "$function_path"
    
    # Create dist directory if it doesn't exist
    mkdir -p dist
    
    # Start build in watch mode (background process)
    npm run build > "../logs/${function_name}-build.log" 2>&1 &
    local build_pid=$!
    
    log_info "Hot reload build started for $function_name (PID: $build_pid)"
    
    # Wait a moment for initial build
    sleep 3
    
    cd - > /dev/null
    echo $build_pid
}

# Function to deploy a Lambda function with hot reload
deploy_lambda_with_hot_reload() {
    local function_name=$1
    local function_path=$2
    local handler=${3:-"index.handler"}
    local description=$4
    
    log_info "Deploying $function_name with hot reload..."
    
    local dist_path="$function_path/dist"
    local absolute_dist_path=$(realpath "$dist_path")
    
    # Check if dist directory exists and has index.js
    if [ ! -f "$dist_path/index.js" ]; then
        log_error "Built Lambda function not found at $dist_path/index.js"
        return 1
    fi
    
    # Get Cognito information for environment variables
    local user_pool_id=""
    local user_pool_client_id=""
    
    user_pool_id=$(awslocal cognito-idp list-user-pools --max-results 50 \
        --query 'UserPools[?contains(Name, `GameFlex`) || contains(Name, `gameflex`)].Id' \
        --output text 2>/dev/null | head -n1 || echo "")
    
    if [ -n "$user_pool_id" ] && [ "$user_pool_id" != "None" ]; then
        user_pool_client_id=$(awslocal cognito-idp list-user-pool-clients --user-pool-id "$user_pool_id" \
            --query 'UserPoolClients[0].ClientId' \
            --output text 2>/dev/null || echo "")
    fi
    
    # Check if function exists
    if awslocal lambda get-function --function-name "$function_name" > /dev/null 2>&1; then
        log_info "Function $function_name exists, updating..."
        
        # Update existing function with hot reload
        awslocal lambda update-function-code \
            --function-name "$function_name" \
            --code "S3Bucket=hot-reload,S3Key=$absolute_dist_path" > /dev/null
        
        awslocal lambda update-function-configuration \
            --function-name "$function_name" \
            --handler "$handler" \
            --environment "Variables={COGNITO_USER_POOL_ID=$user_pool_id,COGNITO_USER_POOL_CLIENT_ID=$user_pool_client_id,AWS_ENDPOINT_URL=http://localhost:45660,AWS_DEFAULT_REGION=us-east-1}" > /dev/null
        
        log_info "Successfully updated $function_name with hot reload"
    else
        log_info "Function $function_name does not exist, creating..."
        
        # Create new function with hot reload
        awslocal lambda create-function \
            --function-name "$function_name" \
            --runtime nodejs18.x \
            --role "arn:aws:iam::000000000000:role/lambda-role" \
            --handler "$handler" \
            --code "S3Bucket=hot-reload,S3Key=$absolute_dist_path" \
            --description "$description" \
            --environment "Variables={COGNITO_USER_POOL_ID=$user_pool_id,COGNITO_USER_POOL_CLIENT_ID=$user_pool_client_id,AWS_ENDPOINT_URL=http://localhost:45660,AWS_DEFAULT_REGION=us-east-1}" > /dev/null
        
        log_info "Successfully created $function_name with hot reload"
    fi
}

# Parse command line arguments
ENVIRONMENT="development"
BUILD_ONLY=false
SKIP_INSTALL=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --environment)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --build-only)
            BUILD_ONLY=true
            shift
            ;;
        --skip-install)
            SKIP_INSTALL=true
            shift
            ;;
        *)
            echo "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if LocalStack is running
if ! check_localstack; then
    log_error "LocalStack is not running. Please start LocalStack first."
    exit 1
fi

log_info "LocalStack is running"

# Ensure IAM role exists for Lambda functions
log_info "Ensuring Lambda execution role exists..."
if awslocal iam get-role --role-name lambda-role > /dev/null 2>&1; then
    log_info "Lambda execution role already exists"
else
    log_info "Creating Lambda execution role..."
    
    cat > /tmp/lambda-trust-policy.json << EOF
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Effect": "Allow",
            "Principal": {
                "Service": "lambda.amazonaws.com"
            },
            "Action": "sts:AssumeRole"
        }
    ]
}
EOF
    
    awslocal iam create-role \
        --role-name lambda-role \
        --assume-role-policy-document file:///tmp/lambda-trust-policy.json > /dev/null
    
    awslocal iam attach-role-policy \
        --role-name lambda-role \
        --policy-arn arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole > /dev/null
    
    rm -f /tmp/lambda-trust-policy.json
    log_info "Lambda execution role created"
fi

# Create logs directory for build outputs
mkdir -p logs

# Get all Lambda function directories
lambda_functions=($(find lambda-functions -maxdepth 1 -type d -exec test -f {}/package.json \; -print | sort))

if [ ${#lambda_functions[@]} -eq 0 ]; then
    log_warn "No Lambda functions found with package.json"
    exit 0
fi

log_info "Found ${#lambda_functions[@]} Lambda functions"

build_pids=()
deployment_errors=()

# Install dependencies and build functions
for function_path in "${lambda_functions[@]}"; do
    function_name=$(basename "$function_path")
    
    log_info "Processing $function_name..."
    
    # Install dependencies
    if [ "$SKIP_INSTALL" != "true" ]; then
        if ! install_lambda_dependencies "$function_path"; then
            deployment_errors+=("Failed to install dependencies for $function_name")
            continue
        fi
    fi
    
    if [ "$BUILD_ONLY" = "true" ]; then
        # Just build once
        if ! build_lambda_function "$function_path"; then
            deployment_errors+=("Failed to build $function_name")
        fi
    else
        # Start hot reload build
        build_pid=$(start_hot_reload_build "$function_path")
        if [ -n "$build_pid" ]; then
            build_pids+=("$function_name:$build_pid")
        else
            deployment_errors+=("Failed to start hot reload build for $function_name")
        fi
    fi
done

if [ "$BUILD_ONLY" = "true" ]; then
    if [ ${#deployment_errors[@]} -gt 0 ]; then
        log_warn "Some builds failed:"
        for error in "${deployment_errors[@]}"; do
            log_warn "  - $error"
        done
        exit 1
    else
        log_info "All Lambda functions built successfully"
        exit 0
    fi
fi

# Wait for initial builds to complete
log_info "Waiting for initial builds to complete..."
sleep 5

# Deploy functions with hot reload
for function_path in "${lambda_functions[@]}"; do
    function_name=$(basename "$function_path")
    lambda_function_name="gameflex-$function_name-$ENVIRONMENT"
    
    if ! deploy_lambda_with_hot_reload "$lambda_function_name" "$function_path" "index.handler" "GameFlex $function_name functions with hot reload"; then
        deployment_errors+=("Failed to deploy $function_name")
    fi
done

# Report results
if [ ${#deployment_errors[@]} -gt 0 ]; then
    log_warn "Some deployments failed:"
    for error in "${deployment_errors[@]}"; do
        log_warn "  - $error"
    done
else
    log_info "All Lambda functions deployed successfully with hot reload!"
fi

log_header "Hot reload deployment completed!"
log_info "Lambda functions are now running with hot reload enabled"
log_info "Build processes running: ${#build_pids[@]}"

if [ ${#build_pids[@]} -gt 0 ]; then
    echo ""
    echo -e "${YELLOW}🔥 Hot reload is active! Your Lambda functions will automatically update when you save changes.${NC}"
    echo -e "${YELLOW}📝 Edit your TypeScript files in the src/ directories and they will be automatically recompiled and reloaded.${NC}"
    echo ""
    log_info "Build processes (you can stop them by running 'pkill -f esbuild' or closing this terminal):"
    for build_info in "${build_pids[@]}"; do
        IFS=':' read -r name pid <<< "$build_info"
        echo -e "  ${CYAN}🔧 $name (PID: $pid)${NC}"
    done
    echo ""
    log_info "Build logs are available in the logs/ directory"
fi

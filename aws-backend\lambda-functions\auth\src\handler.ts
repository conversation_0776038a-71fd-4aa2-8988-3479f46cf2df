/**
 * GameFlex Authentication Lambda Functions
 * Handles user authentication using AWS Cognito
 */

import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { CognitoIdentityProviderClient, AdminCreateUserCommand, AdminSetUserPasswordCommand, AdminInitiateAuthCommand, GetUserCommand, GlobalSignOutCommand, ForgotPasswordCommand, ConfirmForgotPasswordCommand } from '@aws-sdk/client-cognito-identity-provider';
import { DynamoDBClient } from '@aws-sdk/client-dynamodb';
import { DynamoDBDocumentClient, GetCommand, PutCommand, QueryCommand, UpdateCommand } from '@aws-sdk/lib-dynamodb';
import { v4 as uuidv4 } from 'uuid';
import * as jwt from 'jsonwebtoken';

// AWS clients
const cognitoClient = new CognitoIdentityProviderClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

// DynamoDB configuration
const dynamoClient = new DynamoDBClient({
    endpoint: process.env.AWS_ENDPOINT_URL || 'http://localhost:45660',
    region: process.env.AWS_DEFAULT_REGION || 'us-east-1',
    credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test'
    }
});

const docClient = DynamoDBDocumentClient.from(dynamoClient);

// Cognito configuration
const USER_POOL_ID = process.env.COGNITO_USER_POOL_ID || '';
const CLIENT_ID = process.env.COGNITO_USER_POOL_CLIENT_ID || '';

// Table names
const USERS_TABLE = process.env.DYNAMODB_TABLE_USERS || 'Users';

interface User {
    id: string;
    cognito_user_id: string;
    email: string;
    username: string;
    display_name?: string;
    avatar_url?: string;
    is_active: boolean;
    is_verified: boolean;
    created_at: Date;
    updated_at: Date;
    last_login?: Date;
}

function createCorsResponse(statusCode: number, body: any): APIGatewayProxyResult {
    return {
        statusCode,
        headers: {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization, X-Amz-Date, X-Api-Key, X-Amz-Security-Token'
        },
        body: JSON.stringify(body)
    };
}

// JWT Token Validation Utilities
interface TokenPayload {
    sub: string;
    email: string;
    username?: string;
    aud: string;
    iss: string;
    exp: number;
    iat: number;
    token_use: string;
}

interface AuthContext {
    userId: string;
    cognitoUserId: string;
    email: string;
    username?: string;
    isAuthenticated: boolean;
}

/**
 * Extract and validate JWT token from Authorization header
 */
function extractTokenFromHeader(authHeader?: string): string | null {
    if (!authHeader) {
        return null;
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
        return null;
    }

    return parts[1];
}

/**
 * Validate JWT token using Cognito public keys (simplified for LocalStack)
 * In production, this would verify against Cognito's public keys
 */
async function validateJwtToken(token: string): Promise<TokenPayload | null> {
    try {
        // For LocalStack development, we'll do basic JWT parsing without signature verification
        // In production, you would verify against Cognito's public keys
        const decoded = jwt.decode(token) as TokenPayload;

        if (!decoded || !decoded.sub) {
            return null;
        }

        // Check if token is expired
        const now = Math.floor(Date.now() / 1000);
        if (decoded.exp && decoded.exp < now) {
            return null;
        }

        // Verify token is an access token (be more lenient for LocalStack)
        if (decoded.token_use && decoded.token_use !== 'access') {
            return null;
        }
        return decoded;
    } catch (error) {
        console.error('JWT validation error:', error);
        return null;
    }
}

/**
 * Get user context from JWT token
 */
async function getUserFromToken(token: string): Promise<AuthContext | null> {
    const payload = await validateJwtToken(token);
    if (!payload) {
        return null;
    }

    try {
        // Get user from database using Cognito user ID
        const user = await getUserByCognitoId(payload.sub);
        if (!user) {
            return null;
        }

        return {
            userId: user.id,
            cognitoUserId: payload.sub,
            email: payload.email || user.email,
            username: user.username,
            isAuthenticated: true
        };
    } catch (error) {
        console.error('Error getting user from token:', error);
        return null;
    }
}

/**
 * Middleware function to authenticate requests
 */
async function authenticateRequest(event: APIGatewayProxyEvent): Promise<{ success: boolean; authContext?: AuthContext; error?: string }> {
    const authHeader = event.headers?.Authorization || event.headers?.authorization;
    const token = extractTokenFromHeader(authHeader);

    if (!token) {
        return { success: false, error: 'Authorization header required' };
    }

    const authContext = await getUserFromToken(token);
    if (!authContext) {
        return { success: false, error: 'Invalid or expired token' };
    }

    return { success: true, authContext };
}

async function createUserRecord(cognitoUserId: string, email: string, username: string): Promise<string | null> {
    try {
        const userId = uuidv4();
        const now = new Date().toISOString();

        // First check if user already exists
        const existingUser = await getUserByCognitoId(cognitoUserId);
        if (existingUser) {
            // Update existing user
            const updateCommand = new UpdateCommand({
                TableName: USERS_TABLE,
                Key: { id: existingUser.id },
                UpdateExpression: 'SET email = :email, username = :username, updated_at = :updated_at',
                ExpressionAttributeValues: {
                    ':email': email,
                    ':username': username,
                    ':updated_at': now
                }
            });

            await docClient.send(updateCommand);
            return existingUser.id;
        }

        // Create new user
        const putCommand = new PutCommand({
            TableName: USERS_TABLE,
            Item: {
                id: userId,
                cognito_user_id: cognitoUserId,
                email: email,
                username: username,
                display_name: username,
                is_active: true,
                is_verified: false,
                created_at: now,
                updated_at: now
            }
        });

        await docClient.send(putCommand);
        return userId;
    } catch (error) {
        console.error('Failed to create user record:', error);
        return null;
    }
}

async function getUserByCognitoId(cognitoUserId: string): Promise<User | null> {
    try {
        const queryCommand = new QueryCommand({
            TableName: USERS_TABLE,
            IndexName: 'CognitoUserIdIndex',
            KeyConditionExpression: 'cognito_user_id = :cognitoUserId',
            ExpressionAttributeValues: {
                ':cognitoUserId': cognitoUserId
            }
        });

        const result = await docClient.send(queryCommand);
        return result.Items?.[0] as User || null;
    } catch (error) {
        console.error('Failed to get user:', error);
        return null;
    }
}

async function signupHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        // Only log raw body in non-test environments or for valid JSON
        if (process.env.NODE_ENV !== 'test' || (event.body && !event.body.includes('invalid json'))) {
            console.log('Raw event body:', event.body);
        }

        let body;
        try {
            // Handle potential double-encoding
            let bodyStr = event.body || '{}';
            if (typeof bodyStr === 'string' && bodyStr.startsWith('"') && bodyStr.endsWith('"')) {
                // If the body is double-encoded as a JSON string, parse it twice
                bodyStr = JSON.parse(bodyStr);
            }
            body = JSON.parse(bodyStr);
        } catch (parseError) {
            // Only log detailed errors in non-test environments or for unexpected errors
            if (process.env.NODE_ENV !== 'test' || !event.body?.includes('invalid json')) {
                console.error('JSON parse error:', parseError);
                console.error('Body content:', event.body);
            }
            return createCorsResponse(400, {
                error: 'Invalid JSON in request body'
            });
        }

        const email = body.email?.trim().toLowerCase();
        const password = body.password;
        const username = body.username?.trim();

        if (!email || !password || !username) {
            return createCorsResponse(400, {
                error: 'Email, password, and username are required'
            });
        }

        try {
            // Create user in Cognito
            const createUserCommand = new AdminCreateUserCommand({
                UserPoolId: USER_POOL_ID,
                Username: email,
                UserAttributes: [
                    { Name: 'email', Value: email },
                    { Name: 'email_verified', Value: 'true' }
                ],
                TemporaryPassword: password,
                MessageAction: 'SUPPRESS'
            });

            const response = await cognitoClient.send(createUserCommand);
            const cognitoUserId = response.User?.Username || '';

            // Set permanent password
            const setPasswordCommand = new AdminSetUserPasswordCommand({
                UserPoolId: USER_POOL_ID,
                Username: cognitoUserId,
                Password: password,
                Permanent: true
            });

            await cognitoClient.send(setPasswordCommand);

            // Create user record in database
            const userId = await createUserRecord(cognitoUserId, email, username);
            if (!userId) {
                return createCorsResponse(500, {
                    error: 'Failed to create user profile'
                });
            }

            return createCorsResponse(201, {
                message: 'User created successfully',
                user: {
                    id: userId,
                    email,
                    username
                }
            });

        } catch (error: any) {
            if (error.name === 'UsernameExistsException') {
                return createCorsResponse(409, {
                    error: 'User already exists'
                });
            }
            console.error('Cognito signup error:', error);
            return createCorsResponse(500, {
                error: 'Failed to create user account'
            });
        }

    } catch (error) {
        console.error('Signup handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function signinHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        // Only log raw body in non-test environments or for valid JSON
        if (process.env.NODE_ENV !== 'test' || (event.body && !event.body.includes('invalid json'))) {
            console.log('Raw event body:', event.body);
        }

        let body;
        try {
            // Handle potential double-encoding
            let bodyStr = event.body || '{}';
            if (typeof bodyStr === 'string' && bodyStr.startsWith('"') && bodyStr.endsWith('"')) {
                // If the body is double-encoded as a JSON string, parse it twice
                bodyStr = JSON.parse(bodyStr);
            }
            body = JSON.parse(bodyStr);
        } catch (parseError) {
            console.error('JSON parse error:', parseError);
            console.error('Body content:', event.body);
            return createCorsResponse(400, {
                error: 'Invalid JSON in request body'
            });
        }

        const email = body.email?.trim().toLowerCase();
        const password = body.password;

        if (!email || !password) {
            return createCorsResponse(400, {
                error: 'Email and password are required'
            });
        }

        try {
            // Authenticate with Cognito
            const authCommand = new AdminInitiateAuthCommand({
                UserPoolId: USER_POOL_ID,
                ClientId: CLIENT_ID,
                AuthFlow: 'ADMIN_NO_SRP_AUTH',
                AuthParameters: {
                    USERNAME: email,
                    PASSWORD: password
                }
            });

            const response = await cognitoClient.send(authCommand);
            const authResult = response.AuthenticationResult;

            if (!authResult) {
                return createCorsResponse(401, {
                    error: 'Authentication failed'
                });
            }

            const accessToken = authResult.AccessToken!;
            const refreshToken = authResult.RefreshToken!;
            const idToken = authResult.IdToken!;

            // Get user info from Cognito
            const getUserCommand = new GetUserCommand({
                AccessToken: accessToken
            });

            const userResponse = await cognitoClient.send(getUserCommand);
            const cognitoUserId = userResponse.Username!;

            // Get user from database
            const user = await getUserByCognitoId(cognitoUserId);
            if (!user) {
                return createCorsResponse(404, {
                    error: 'User profile not found'
                });
            }

            // Update last login
            try {
                await docClient.send(new UpdateCommand({
                    TableName: USERS_TABLE,
                    Key: { id: user.id },
                    UpdateExpression: 'SET last_login = :lastLogin',
                    ExpressionAttributeValues: {
                        ':lastLogin': new Date().toISOString()
                    }
                }));
            } catch (error) {
                console.warn('Failed to update last login:', error);
            }

            return createCorsResponse(200, {
                message: 'Authentication successful',
                tokens: {
                    access_token: accessToken,
                    refresh_token: refreshToken,
                    id_token: idToken
                },
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    display_name: user.display_name,
                    avatar_url: user.avatar_url,
                    is_verified: user.is_verified
                }
            });

        } catch (error: any) {
            if (error.name === 'NotAuthorizedException' || error.name === 'UserNotFoundException') {
                return createCorsResponse(401, {
                    error: 'Invalid email or password'
                });
            }
            console.error('Cognito signin error:', error);
            return createCorsResponse(500, {
                error: 'Authentication failed'
            });
        }

    } catch (error) {
        console.error('Signin handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function refreshTokenHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const body = JSON.parse(event.body || '{}');
        const refreshToken = body.refresh_token;

        if (!refreshToken) {
            return createCorsResponse(400, {
                error: 'Refresh token is required'
            });
        }

        try {
            const authCommand = new AdminInitiateAuthCommand({
                UserPoolId: USER_POOL_ID,
                ClientId: CLIENT_ID,
                AuthFlow: 'REFRESH_TOKEN_AUTH',
                AuthParameters: {
                    REFRESH_TOKEN: refreshToken
                }
            });

            const response = await cognitoClient.send(authCommand);
            const authResult = response.AuthenticationResult;

            if (!authResult) {
                return createCorsResponse(401, {
                    error: 'Invalid refresh token'
                });
            }

            return createCorsResponse(200, {
                message: 'Token refreshed successfully',
                tokens: {
                    access_token: authResult.AccessToken!,
                    id_token: authResult.IdToken!
                }
            });

        } catch (error) {
            console.error('Token refresh error:', error);
            return createCorsResponse(401, {
                error: 'Invalid refresh token'
            });
        }

    } catch (error) {
        console.error('Refresh token handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function signoutHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const authHeader = event.headers?.Authorization || event.headers?.authorization || '';

        if (!authHeader.startsWith('Bearer ')) {
            return createCorsResponse(401, {
                error: 'Authorization header required'
            });
        }

        const accessToken = authHeader.substring(7);

        try {
            const signOutCommand = new GlobalSignOutCommand({
                AccessToken: accessToken
            });

            await cognitoClient.send(signOutCommand);

            return createCorsResponse(200, {
                message: 'Signed out successfully'
            });

        } catch (error) {
            console.error('Signout error:', error);
            return createCorsResponse(500, {
                error: 'Failed to sign out'
            });
        }

    } catch (error) {
        console.error('Signout handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function forgotPasswordHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const body = JSON.parse(event.body || '{}');
        const email = body.email?.trim().toLowerCase();

        if (!email) {
            return createCorsResponse(400, {
                error: 'Email is required'
            });
        }

        try {
            const forgotPasswordCommand = new ForgotPasswordCommand({
                ClientId: CLIENT_ID,
                Username: email
            });

            await cognitoClient.send(forgotPasswordCommand);

            return createCorsResponse(200, {
                message: 'Password reset code sent to your email'
            });

        } catch (error: any) {
            if (error.name === 'UserNotFoundException') {
                // For security, don't reveal if user exists or not
                return createCorsResponse(200, {
                    message: 'Password reset code sent to your email'
                });
            }

            // Handle LocalStack-specific errors gracefully
            if (error.name === 'InternalError' || error.$metadata?.httpStatusCode === 500) {
                // LocalStack bug - return success for security (don't reveal user existence)
                return createCorsResponse(200, {
                    message: 'Password reset code sent to your email'
                });
            }

            console.error('Forgot password error:', error);
            // For security, don't reveal if user exists or not
            return createCorsResponse(200, {
                message: 'Password reset code sent to your email'
            });
        }

    } catch (error) {
        console.error('Forgot password handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function resetPasswordHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        const body = JSON.parse(event.body || '{}');
        const email = body.email?.trim().toLowerCase();
        const confirmationCode = body.confirmation_code;
        const newPassword = body.new_password;

        if (!email || !confirmationCode || !newPassword) {
            return createCorsResponse(400, {
                error: 'Email, confirmation code, and new password are required'
            });
        }

        try {
            const confirmForgotPasswordCommand = new ConfirmForgotPasswordCommand({
                ClientId: CLIENT_ID,
                Username: email,
                ConfirmationCode: confirmationCode,
                Password: newPassword
            });

            await cognitoClient.send(confirmForgotPasswordCommand);

            return createCorsResponse(200, {
                message: 'Password reset successfully'
            });

        } catch (error: any) {
            if (error.name === 'CodeMismatchException') {
                return createCorsResponse(400, {
                    error: 'Invalid confirmation code'
                });
            }
            if (error.name === 'ExpiredCodeException') {
                return createCorsResponse(400, {
                    error: 'Confirmation code has expired'
                });
            }
            if (error.name === 'InvalidPasswordException') {
                return createCorsResponse(400, {
                    error: 'Password does not meet requirements'
                });
            }
            console.error('Reset password error:', error);
            return createCorsResponse(500, {
                error: 'Failed to reset password'
            });
        }

    } catch (error) {
        console.error('Reset password handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function getProfileHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        // Authenticate the request
        const authResult = await authenticateRequest(event);
        if (!authResult.success) {
            return createCorsResponse(401, {
                error: authResult.error
            });
        }

        const authContext = authResult.authContext!;

        try {
            // Get user from database
            const getCommand = new GetCommand({
                TableName: USERS_TABLE,
                Key: { id: authContext.userId }
            });

            const result = await docClient.send(getCommand);
            const user = result.Item as User;

            if (!user) {
                return createCorsResponse(404, {
                    error: 'User profile not found'
                });
            }

            return createCorsResponse(200, {
                user: {
                    id: user.id,
                    email: user.email,
                    username: user.username,
                    display_name: user.display_name,
                    avatar_url: user.avatar_url,
                    is_verified: user.is_verified,
                    created_at: user.created_at,
                    updated_at: user.updated_at,
                    last_login: user.last_login
                }
            });

        } catch (error) {
            console.error('Get profile error:', error);
            return createCorsResponse(500, {
                error: 'Failed to get user profile'
            });
        }

    } catch (error) {
        console.error('Get profile handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

async function updateProfileHandler(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
        // Authenticate the request
        const authResult = await authenticateRequest(event);
        if (!authResult.success) {
            return createCorsResponse(401, {
                error: authResult.error
            });
        }

        const authContext = authResult.authContext!;
        const body = JSON.parse(event.body || '{}');

        // Validate allowed fields
        const allowedFields = ['display_name', 'avatar_url'];
        const updates: any = {};
        let hasUpdates = false;

        for (const field of allowedFields) {
            if (body[field] !== undefined) {
                updates[field] = body[field];
                hasUpdates = true;
            }
        }

        if (!hasUpdates) {
            return createCorsResponse(400, {
                error: 'No valid fields to update'
            });
        }

        try {
            // Build update expression
            const updateExpressions: string[] = [];
            const expressionAttributeValues: any = {};

            Object.keys(updates).forEach((key, index) => {
                updateExpressions.push(`${key} = :val${index}`);
                expressionAttributeValues[`:val${index}`] = updates[key];
            });

            // Add updated_at timestamp
            updateExpressions.push('updated_at = :updated_at');
            expressionAttributeValues[':updated_at'] = new Date().toISOString();

            const updateCommand = new UpdateCommand({
                TableName: USERS_TABLE,
                Key: { id: authContext.userId },
                UpdateExpression: `SET ${updateExpressions.join(', ')}`,
                ExpressionAttributeValues: expressionAttributeValues,
                ReturnValues: 'ALL_NEW'
            });

            const result = await docClient.send(updateCommand);
            const updatedUser = result.Attributes as User;

            return createCorsResponse(200, {
                message: 'Profile updated successfully',
                user: {
                    id: updatedUser.id,
                    email: updatedUser.email,
                    username: updatedUser.username,
                    display_name: updatedUser.display_name,
                    avatar_url: updatedUser.avatar_url,
                    is_verified: updatedUser.is_verified,
                    updated_at: updatedUser.updated_at
                }
            });

        } catch (error) {
            console.error('Update profile error:', error);
            return createCorsResponse(500, {
                error: 'Failed to update user profile'
            });
        }

    } catch (error) {
        console.error('Update profile handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
}

export const handler = async (event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> => {
    console.log('🔥 Hot reload is working! Auth Lambda function called');

    // Test hot reload functionality
    if (event.path === '/test-hot-reload') {
        return createCorsResponse(200, {
            message: 'Hot reload is working! This message was updated automatically.',
            timestamp: new Date().toISOString()
        });
    }

    // Handle CORS preflight requests
    if (event.httpMethod === 'OPTIONS') {
        return createCorsResponse(200, {});
    }

    const path = event.path;
    const method = event.httpMethod;

    try {
        // Authentication endpoints
        if (path === '/auth/signup' && method === 'POST') {
            return await signupHandler(event);
        } else if (path === '/auth/signin' && method === 'POST') {
            return await signinHandler(event);
        } else if (path === '/auth/refresh' && method === 'POST') {
            return await refreshTokenHandler(event);
        } else if (path === '/auth/signout' && method === 'POST') {
            return await signoutHandler(event);
        }
        // Password reset endpoints
        else if (path === '/auth/forgot-password' && method === 'POST') {
            return await forgotPasswordHandler(event);
        } else if (path === '/auth/reset-password' && method === 'POST') {
            return await resetPasswordHandler(event);
        }
        // Profile management endpoints
        else if (path === '/auth/profile' && method === 'GET') {
            return await getProfileHandler(event);
        } else if (path === '/auth/profile' && method === 'PUT') {
            return await updateProfileHandler(event);
        } else {
            return createCorsResponse(404, {
                error: 'Endpoint not found'
            });
        }
    } catch (error) {
        console.error('Lambda handler error:', error);
        return createCorsResponse(500, {
            error: 'Internal server error'
        });
    }
};

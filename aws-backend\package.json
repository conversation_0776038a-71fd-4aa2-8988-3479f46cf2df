{"name": "gameflex-aws-backend", "version": "1.0.0", "description": "GameFlex AWS Backend with LocalStack Testing", "main": "index.js", "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --testPathPattern=integration", "test:unit": "jest --testPathPattern=unit", "test:e2e": "jest --testPathPattern=e2e", "test:api": "jest --testPathPattern=api", "test:all": "npm run test:unit && npm run test:integration && npm run test:api && npm run test:e2e", "test:localstack": "npm run test:integration && npm run test:api && npm run test:e2e", "build": "npm run build:auth && npm run build:posts && npm run build:media && npm run build:users", "build:auth": "cd lambda-functions/auth && npm run build-once", "build:posts": "cd lambda-functions/posts && npm run build-once", "build:media": "cd lambda-functions/media && npm run build-once", "build:users": "cd lambda-functions/users && npm run build-once", "install:all": "npm install && npm run install:lambdas", "install:lambdas": "cd lambda-functions/auth && npm install && cd ../posts && npm install && cd ../media && npm install && cd ../users && npm install"}, "devDependencies": {"@aws-sdk/client-api-gateway": "^3.450.0", "@aws-sdk/client-cognito-identity-provider": "^3.450.0", "@aws-sdk/client-dynamodb": "^3.450.0", "@aws-sdk/client-lambda": "^3.450.0", "@aws-sdk/client-s3": "^3.450.0", "@aws-sdk/lib-dynamodb": "^3.450.0", "@types/aws-lambda": "^8.10.150", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^20.19.7", "@types/supertest": "^6.0.2", "aws-sdk-client-mock": "^3.0.1", "aws-sdk-client-mock-jest": "^3.0.1", "jest": "^29.7.0", "jest-junit": "^16.0.0", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "typescript": "^5.8.3"}, "dependencies": {"axios": "^1.6.7", "uuid": "^9.0.1"}, "engines": {"node": ">=18.0.0"}}
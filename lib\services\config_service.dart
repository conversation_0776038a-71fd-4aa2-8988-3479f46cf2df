import 'dart:developer' as developer;
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Configuration service for managing app settings and server URLs
class ConfigService {
  static ConfigService? _instance;
  static ConfigService get instance => _instance ??= ConfigService._();

  ConfigService._();

  // Environment configuration
  static const bool _isStaging = bool.fromEnvironment(
    'STAGING',
    defaultValue: false,
  );

  static const bool _isDevelopment = bool.fromEnvironment(
    'DEVELOPMENT',
    defaultValue: true,
  );

  // Configuration keys
  static const String _serverUrlKey = 'gameflex_server_url';
  static const String _autoDetectKey = 'gameflex_auto_detect_url';
  static const String _selectedUrlOptionKey = 'gameflex_selected_url_option';

  // Server URL options
  static const String _localhostUrl = 'http://127.0.0.1:45660';
  static const String _androidEmulatorUrl = 'http://********:45660';
  static const String _stagingUrl = 'http://dev.api.gameflex.io:8000';
  static const String _productionUrl = 'https://api.gameflex.io';

  String? _cachedServerUrl;

  /// Check if app is in development mode
  bool get isDevelopment => _isDevelopment && !_isStaging;

  /// Check if app is in staging mode
  bool get isStaging => _isStaging;

  /// Check if app is in production mode
  bool get isProduction => !_isDevelopment && !_isStaging;

  /// Get the current server URL based on environment and user preferences
  Future<String> getServerUrl() async {
    if (_cachedServerUrl != null) {
      return _cachedServerUrl!;
    }

    // In production mode, always use production URL
    if (isProduction) {
      _cachedServerUrl = _productionUrl;
      return _cachedServerUrl!;
    }

    // In staging mode, always use staging URL
    if (isStaging) {
      _cachedServerUrl = _stagingUrl;
      return _cachedServerUrl!;
    }

    // In development mode, check user preferences
    final prefs = await SharedPreferences.getInstance();
    final autoDetect = prefs.getBool(_autoDetectKey) ?? true;

    if (autoDetect) {
      _cachedServerUrl = await _getAutoDetectedUrl();
    } else {
      _cachedServerUrl =
          prefs.getString(_serverUrlKey) ?? await _getAutoDetectedUrl();
    }

    developer.log('ConfigService: Using server URL: $_cachedServerUrl');
    return _cachedServerUrl!;
  }

  /// Auto-detect the appropriate server URL based on platform
  Future<String> _getAutoDetectedUrl() async {
    if (kIsWeb) {
      return _localhostUrl;
    } else if (defaultTargetPlatform == TargetPlatform.android) {
      // Check if running on emulator
      if (await isRunningOnEmulator()) {
        return _androidEmulatorUrl;
      } else {
        // Running on physical device, use localhost
        return _localhostUrl;
      }
    } else {
      // Windows, iOS, macOS, Linux - use localhost
      return _localhostUrl;
    }
  }

  /// Check if running on Android emulator
  Future<bool> isRunningOnEmulator() async {
    if (!Platform.isAndroid) return false;

    try {
      // Method 1: Check for emulator characteristics using getprop
      final result = await Process.run('getprop', ['ro.kernel.qemu']);
      if (result.stdout.toString().trim() == '1') {
        return true;
      }

      // Method 2: Check for emulator-specific properties
      final buildResult = await Process.run('getprop', [
        'ro.build.fingerprint',
      ]);
      final fingerprint = buildResult.stdout.toString().toLowerCase();
      if (fingerprint.contains('generic') || fingerprint.contains('emulator')) {
        return true;
      }

      // Method 3: Check hardware name
      final hardwareResult = await Process.run('getprop', ['ro.hardware']);
      final hardware = hardwareResult.stdout.toString().toLowerCase();
      if (hardware.contains('goldfish') || hardware.contains('ranchu')) {
        return true;
      }

      return false;
    } catch (e) {
      // Fallback: assume emulator if we can't determine
      developer.log(
        'ConfigService: Could not determine if running on emulator: $e',
      );
      return true;
    }
  }

  /// Set server URL manually (for development dropdown)
  Future<void> setServerUrl(String url, String optionName) async {
    if (!isDevelopment) {
      developer.log(
        'ConfigService: Cannot change server URL in non-development mode',
      );
      return;
    }

    final prefs = await SharedPreferences.getInstance();

    if (url == 'auto') {
      await prefs.setBool(_autoDetectKey, true);
      await prefs.remove(_serverUrlKey);
      await prefs.setString(_selectedUrlOptionKey, optionName);
      _cachedServerUrl = null; // Reset cache to trigger auto-detection
    } else {
      await prefs.setString(_serverUrlKey, url);
      await prefs.setBool(_autoDetectKey, false);
      await prefs.setString(_selectedUrlOptionKey, optionName);
      _cachedServerUrl = url;
    }

    developer.log('ConfigService: Server URL set to: $url ($optionName)');
  }

  /// Get the currently selected URL option name
  Future<String> getSelectedUrlOption() async {
    if (!isDevelopment) {
      return isStaging ? 'Staging' : 'Production';
    }

    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_selectedUrlOptionKey) ?? 'Auto-detect';
  }

  /// Enable auto-detection of server URL
  Future<void> enableAutoDetection() async {
    if (!isDevelopment) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_autoDetectKey, true);
    await prefs.remove(_serverUrlKey);
    await prefs.setString(_selectedUrlOptionKey, 'Auto-detect');
    _cachedServerUrl = null; // Reset cache to trigger auto-detection

    developer.log('ConfigService: Auto-detection enabled');
  }

  /// Check if auto-detection is enabled
  Future<bool> isAutoDetectionEnabled() async {
    if (!isDevelopment) return false;

    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_autoDetectKey) ?? true;
  }

  /// Get available server URL options for development dropdown
  List<ServerUrlOption> getServerUrlOptions() {
    if (!isDevelopment) {
      // In production/staging, only show current environment
      return [
        ServerUrlOption(
          name: isStaging ? 'Staging' : 'Production',
          url: isStaging ? _stagingUrl : _productionUrl,
          description:
              isStaging
                  ? 'Development staging environment'
                  : 'Production environment',
        ),
      ];
    }

    return [
      ServerUrlOption(
        name: 'Auto-detect',
        url: 'auto',
        description: 'Automatically detect based on platform',
      ),
      ServerUrlOption(
        name: 'Localhost',
        url: _localhostUrl,
        description: 'Local development server',
      ),
      ServerUrlOption(
        name: 'Android Emulator',
        url: _androidEmulatorUrl,
        description: 'Android emulator (********)',
      ),
      ServerUrlOption(
        name: 'Staging',
        url: _stagingUrl,
        description: 'Development staging server',
      ),
      ServerUrlOption(
        name: 'Production',
        url: _productionUrl,
        description: 'Production server (HTTPS)',
      ),
    ];
  }

  /// Get environment display name
  String getEnvironmentName() {
    if (isProduction) return 'Production';
    if (isStaging) return 'Staging';
    return 'Development';
  }

  /// Get environment color for UI
  String getEnvironmentColor() {
    if (isProduction) return '#FF5722'; // Red
    if (isStaging) return '#FF9800'; // Orange
    return '#4CAF50'; // Green
  }

  /// Clear cached values (useful for testing or environment changes)
  void clearCache() {
    _cachedServerUrl = null;
  }

  /// Reset all configuration to defaults
  Future<void> resetToDefaults() async {
    if (!isDevelopment) return;

    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_serverUrlKey);
    await prefs.remove(_autoDetectKey);
    await prefs.remove(_selectedUrlOptionKey);

    clearCache();
    developer.log('ConfigService: Configuration reset to defaults');
  }

  /// Get debug information
  Future<Map<String, dynamic>> getDebugInfo() async {
    final prefs = await SharedPreferences.getInstance();

    return {
      'environment': getEnvironmentName(),
      'isDevelopment': isDevelopment,
      'isStaging': isStaging,
      'isProduction': isProduction,
      'currentServerUrl': await getServerUrl(),
      'selectedOption': await getSelectedUrlOption(),
      'autoDetectEnabled': await isAutoDetectionEnabled(),
      'isEmulator': await isRunningOnEmulator(),
      'platform': defaultTargetPlatform.toString(),
      'isWeb': kIsWeb,
      'storedServerUrl': prefs.getString(_serverUrlKey),
      'storedAutoDetect': prefs.getBool(_autoDetectKey),
      'storedSelectedOption': prefs.getString(_selectedUrlOptionKey),
    };
  }
}

/// Server URL option model
class ServerUrlOption {
  final String name;
  final String url;
  final String description;

  ServerUrlOption({
    required this.name,
    required this.url,
    required this.description,
  });

  @override
  String toString() => name;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ServerUrlOption &&
          runtimeType == other.runtimeType &&
          name == other.name &&
          url == other.url;

  @override
  int get hashCode => name.hashCode ^ url.hashCode;
}

# GameFlex AWS Backend Test Suite (PowerShell)
# Comprehensive testing script that integrates with existing infrastructure

param(
    [string]$TestType = "all",
    [switch]$Coverage,
    [switch]$Watch,
    [switch]$Verbose,
    [switch]$SetupOnly,
    [switch]$SkipSetup
)

# Function to print colored output
function Write-Status {
    param([string]$Message)
    Write-Host "[TEST] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARN] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Write-Header {
    param([string]$Message)
    Write-Host "[TEST-SUITE] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[PASS] $Message" -ForegroundColor Green
}

function Write-Failure {
    param([string]$Message)
    Write-Host "[FAIL] $Message" -ForegroundColor Red
}

# Test results tracking
$script:TestResults = @{
    Passed = 0
    Failed = 0
    Skipped = 0
    Tests = @()
}

function Test-Prerequisites {
    Write-Header "Checking Prerequisites"
    
    # Check if Node.js is installed
    try {
        $nodeVersion = node --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "Node.js version: $nodeVersion"
        } else {
            Write-Error "Node.js is not installed or not in PATH"
            return $false
        }
    } catch {
        Write-Error "Node.js is not installed or not in PATH"
        return $false
    }
    
    # Check if npm is installed
    try {
        $npmVersion = npm --version 2>$null
        if ($LASTEXITCODE -eq 0) {
            Write-Status "npm version: $npmVersion"
        } else {
            Write-Error "npm is not installed or not in PATH"
            return $false
        }
    } catch {
        Write-Error "npm is not installed or not in PATH"
        return $false
    }
    
    # Check if LocalStack is running
    try {
        $healthCheck = Invoke-RestMethod -Uri "http://localhost:45660/_localstack/health" -Method Get -TimeoutSec 5
        $runningServices = $healthCheck.services.PSObject.Properties | Where-Object { $_.Value -eq "running" } | Measure-Object
        Write-Status "LocalStack is running with $($runningServices.Count) services"
        
        # Check required services
        $requiredServices = @('lambda', 'apigateway', 'dynamodb', 's3', 'cognito-idp')
        $missingServices = @()
        
        foreach ($service in $requiredServices) {
            if ($healthCheck.services.$service -ne "running") {
                $missingServices += $service
            }
        }
        
        if ($missingServices.Count -gt 0) {
            Write-Warning "Missing required services: $($missingServices -join ', ')"
            Write-Warning "Some tests may fail. Consider running ./start.ps1 first."
        } else {
            Write-Status "All required LocalStack services are running"
        }
        
        return $true
    } catch {
        Write-Error "LocalStack is not running or not accessible at http://localhost:45660"
        Write-Error "Please run './start.ps1' to start LocalStack before running tests"
        return $false
    }
}

function Install-Dependencies {
    Write-Header "Installing Dependencies"
    
    # Install root dependencies
    Write-Status "Installing root test dependencies..."
    npm install
    if ($LASTEXITCODE -ne 0) {
        Write-Error "Failed to install root dependencies"
        return $false
    }
    
    # Install Lambda function dependencies
    $lambdaDirs = @("auth", "posts", "media", "users")
    foreach ($dir in $lambdaDirs) {
        $lambdaPath = "lambda-functions/$dir"
        if (Test-Path $lambdaPath) {
            Write-Status "Installing dependencies for $dir lambda..."
            Push-Location $lambdaPath
            npm install
            if ($LASTEXITCODE -ne 0) {
                Write-Error "Failed to install dependencies for $dir lambda"
                Pop-Location
                return $false
            }
            Pop-Location
        }
    }
    
    Write-Success "All dependencies installed successfully"
    return $true
}

function Build-LambdaFunctions {
    Write-Header "Building Lambda Functions"
    
    $lambdaDirs = @("auth", "posts", "media", "users")
    foreach ($dir in $lambdaDirs) {
        $lambdaPath = "lambda-functions/$dir"
        if (Test-Path $lambdaPath) {
            Write-Status "Building $dir lambda function..."
            Push-Location $lambdaPath
            npm run build-once
            if ($LASTEXITCODE -ne 0) {
                Write-Error "Failed to build $dir lambda function"
                Pop-Location
                return $false
            }
            Pop-Location
        }
    }
    
    Write-Success "All Lambda functions built successfully"
    return $true
}

function Run-Tests {
    param([string]$Type, [bool]$WithCoverage, [bool]$WatchMode)
    
    Write-Header "Running Tests: $Type"
    
    $jestArgs = @()
    
    # Add test type filter
    switch ($Type.ToLower()) {
        "unit" { $jestArgs += "--testPathPattern=unit" }
        "integration" { $jestArgs += "--testPathPattern=integration" }
        "api" { $jestArgs += "--testPathPattern=api" }
        "e2e" { $jestArgs += "--testPathPattern=e2e" }
        "all" { }
        default { 
            Write-Warning "Unknown test type: $Type. Running all tests."
        }
    }
    
    # Add coverage if requested
    if ($WithCoverage) {
        $jestArgs += "--coverage"
    }
    
    # Add watch mode if requested
    if ($WatchMode) {
        $jestArgs += "--watch"
    }
    
    # Add verbose mode if requested
    if ($Verbose) {
        $jestArgs += "--verbose"
    }
    
    # Run Jest
    $jestCommand = "npx jest " + ($jestArgs -join " ")
    Write-Status "Executing: $jestCommand"
    
    Invoke-Expression $jestCommand
    $testExitCode = $LASTEXITCODE
    
    if ($testExitCode -eq 0) {
        Write-Success "Tests completed successfully"
        return $true
    } else {
        Write-Failure "Tests failed with exit code: $testExitCode"
        return $false
    }
}

function Show-TestSummary {
    Write-Host ""
    Write-Header "Test Suite Summary"
    Write-Host ""
    
    if (Test-Path "coverage/lcov-report/index.html") {
        Write-Status "Coverage report generated: coverage/lcov-report/index.html"
    }
    
    if (Test-Path "coverage/coverage-summary.json") {
        try {
            $coverageSummary = Get-Content "coverage/coverage-summary.json" | ConvertFrom-Json
            $totalCoverage = $coverageSummary.total
            
            Write-Host "Coverage Summary:" -ForegroundColor Cyan
            Write-Host "  Lines: $($totalCoverage.lines.pct)%" -ForegroundColor $(if ($totalCoverage.lines.pct -ge 80) { "Green" } else { "Yellow" })
            Write-Host "  Functions: $($totalCoverage.functions.pct)%" -ForegroundColor $(if ($totalCoverage.functions.pct -ge 80) { "Green" } else { "Yellow" })
            Write-Host "  Branches: $($totalCoverage.branches.pct)%" -ForegroundColor $(if ($totalCoverage.branches.pct -ge 80) { "Green" } else { "Yellow" })
            Write-Host "  Statements: $($totalCoverage.statements.pct)%" -ForegroundColor $(if ($totalCoverage.statements.pct -ge 80) { "Green" } else { "Yellow" })
        } catch {
            Write-Warning "Could not parse coverage summary"
        }
    }
}

function Main {
    Write-Header "GameFlex AWS Backend Test Suite"
    Write-Host ""
    Write-Status "Test Type: $TestType"
    Write-Status "Coverage: $Coverage"
    Write-Status "Watch Mode: $Watch"
    Write-Status "Verbose: $Verbose"
    Write-Host ""
    
    # Check prerequisites
    if (-not $SkipSetup) {
        if (-not (Test-Prerequisites)) {
            Write-Error "Prerequisites check failed"
            exit 1
        }
        
        # Install dependencies
        if (-not (Install-Dependencies)) {
            Write-Error "Dependency installation failed"
            exit 1
        }
        
        # Build Lambda functions
        if (-not (Build-LambdaFunctions)) {
            Write-Error "Lambda function build failed"
            exit 1
        }
    }
    
    if ($SetupOnly) {
        Write-Success "Setup completed successfully"
        exit 0
    }
    
    # Run tests
    $testSuccess = Run-Tests -Type $TestType -WithCoverage $Coverage -WatchMode $Watch
    
    # Show summary
    if (-not $Watch) {
        Show-TestSummary
    }
    
    # Exit with appropriate code
    if ($testSuccess) {
        Write-Success "All tests completed successfully!"
        exit 0
    } else {
        Write-Failure "Some tests failed"
        exit 1
    }
}

# Show help if requested
if ($args -contains "--help" -or $args -contains "-h") {
    Write-Host "GameFlex AWS Backend Test Suite"
    Write-Host ""
    Write-Host "Usage: ./test-suite.ps1 [options]"
    Write-Host ""
    Write-Host "Options:"
    Write-Host "  -TestType <type>    Type of tests to run (unit, integration, api, e2e, all) [default: all]"
    Write-Host "  -Coverage           Generate code coverage report"
    Write-Host "  -Watch              Run tests in watch mode"
    Write-Host "  -Verbose            Enable verbose output"
    Write-Host "  -SetupOnly          Only run setup (install dependencies, build functions)"
    Write-Host "  -SkipSetup          Skip setup and run tests directly"
    Write-Host "  -h, --help          Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  ./test-suite.ps1                           # Run all tests"
    Write-Host "  ./test-suite.ps1 -TestType unit            # Run only unit tests"
    Write-Host "  ./test-suite.ps1 -Coverage                 # Run all tests with coverage"
    Write-Host "  ./test-suite.ps1 -TestType api -Watch      # Run API tests in watch mode"
    Write-Host "  ./test-suite.ps1 -SetupOnly                # Only setup, don't run tests"
    exit 0
}

# Run main function
try {
    Main
} catch {
    Write-Error "Test suite execution failed: $_"
    exit 1
}

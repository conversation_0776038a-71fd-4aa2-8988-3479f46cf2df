/**
 * Global setup for Jest tests
 * Ensures LocalStack is running and services are available
 */

const { exec } = require('child_process');
const { promisify } = require('util');

const execAsync = promisify(exec);

async function checkLocalStackHealth() {
  try {
    const { stdout } = await execAsync('curl -s http://localhost:45660/_localstack/health');
    const health = JSON.parse(stdout);

    const requiredServices = ['lambda', 'apigateway', 'dynamodb', 's3', 'cognito-idp'];
    const availableServices = Object.keys(health.services);

    const allServicesRunning = requiredServices.every(service =>
      availableServices.includes(service) && health.services[service] === 'running'
    );

    if (allServicesRunning) {
      console.log('✅ LocalStack health check passed - all required services are running');
      return true;
    } else {
      console.log('❌ LocalStack health check failed - some services are not running');
      console.log('Required services:', requiredServices);
      console.log('Available services:', availableServices);
      return false;
    }
  } catch (error) {
    console.log('❌ LocalStack health check failed - unable to connect');
    console.log('Error:', error);
    return false;
  }
}

async function waitForLocalStack(maxAttempts = 30, delayMs = 2000) {
  console.log('🔄 Waiting for LocalStack to be ready...');

  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    const isHealthy = await checkLocalStackHealth();

    if (isHealthy) {
      console.log(`✅ LocalStack is ready after ${attempt} attempts`);
      return;
    }

    if (attempt < maxAttempts) {
      console.log(`⏳ Attempt ${attempt}/${maxAttempts} failed, retrying in ${delayMs}ms...`);
      await new Promise(resolve => setTimeout(resolve, delayMs));
    }
  }

  throw new Error(`❌ LocalStack failed to become ready after ${maxAttempts} attempts`);
}

async function setupApiGatewayUrl() {
  try {
    console.log('🔍 Discovering API Gateway configuration...');

    // Get all REST APIs
    const { stdout } = await execAsync('awslocal apigateway get-rest-apis');
    const apis = JSON.parse(stdout);

    if (!apis.items || apis.items.length === 0) {
      console.log('⚠️  No API Gateway REST APIs found');
      return;
    }

    // Find the GameFlex API (or use the first one if only one exists)
    let targetApi = apis.items.find((api) =>
      api.name && api.name.toLowerCase().includes('gameflex')
    );

    if (!targetApi && apis.items.length === 1) {
      targetApi = apis.items[0];
    }

    if (!targetApi) {
      console.log('⚠️  GameFlex API Gateway not found');
      return;
    }

    const apiId = targetApi.id;
    console.log(`📡 Found API Gateway: ${targetApi.name} (${apiId})`);

    // Get stages for this API
    const { stdout: stagesOutput } = await execAsync(`awslocal apigateway get-stages --rest-api-id ${apiId}`);
    const stages = JSON.parse(stagesOutput);

    if (!stages.item || stages.item.length === 0) {
      console.log(`⚠️  No stages found for API ${apiId}`);
      return;
    }

    // Use development stage if available, otherwise use the first stage
    let targetStage = stages.item.find((stage) => stage.stageName === 'development');
    if (!targetStage) {
      targetStage = stages.item[0];
    }

    const stageName = targetStage.stageName;
    const apiGatewayUrl = `http://localhost:45660/restapis/${apiId}/${stageName}/_user_request_`;

    // Set the environment variable for other parts of the test suite
    process.env.API_GATEWAY_URL = apiGatewayUrl;

    console.log(`🔗 API Gateway URL: ${apiGatewayUrl}`);
  } catch (error) {
    console.log('⚠️  Failed to discover API Gateway URL:', error.message);
    console.log('   Tests will use fallback URL');
  }
}

module.exports = async function globalSetup() {
  console.log('🚀 Starting GameFlex AWS Backend Test Suite');
  console.log('📋 Environment:', process.env.NODE_ENV);
  console.log('🔗 LocalStack URL:', process.env.AWS_ENDPOINT_URL);

  try {
    await waitForLocalStack();
    await setupApiGatewayUrl();
    console.log('✅ Global setup completed successfully');
  } catch (error) {
    console.error('❌ Global setup failed:', error);
    console.log('');
    console.log('💡 Make sure LocalStack is running:');
    console.log('   cd aws-backend && ./start.ps1');
    console.log('');
    throw error;
  }
}
